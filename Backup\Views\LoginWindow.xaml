<Window x:Class="DatesFactoryManager.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة مصنع التمور"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <materialDesign:DialogHost>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:Card Grid.Row="0" Margin="20,20,20,10">
                <StackPanel Orientation="Vertical" Margin="20">
                    <materialDesign:PackIcon Kind="Factory"
                                           Width="64" Height="64"
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="نظام إدارة مصنع التمور"
                              FontSize="20" FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"/>
                    <TextBlock Text="تسجيل الدخول"
                              FontSize="16"
                              HorizontalAlignment="Center"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Login Form -->
            <materialDesign:Card Grid.Row="1" Margin="20,10,20,10">
                <StackPanel Margin="30" VerticalAlignment="Center">

                    <!-- Username -->
                    <TextBox x:Name="UsernameTextBox"
                            materialDesign:HintAssist.Hint="اسم المستخدم"
                            materialDesign:HintAssist.IsFloating="True"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,20"
                            FontSize="14"/>

                    <!-- Password -->
                    <PasswordBox x:Name="PasswordBox"
                                materialDesign:HintAssist.Hint="كلمة المرور"
                                materialDesign:HintAssist.IsFloating="True"
                                Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                Margin="0,0,0,20"
                                FontSize="14"/>

                    <!-- Remember Me -->
                    <CheckBox x:Name="RememberMeCheckBox"
                             Content="تذكرني"
                             Margin="0,0,0,20"
                             HorizontalAlignment="Right"/>

                    <!-- Login Button -->
                    <Button x:Name="LoginButton"
                           Content="تسجيل الدخول"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Height="40"
                           FontSize="14"
                           Click="LoginButton_Click"
                           IsDefault="True"/>

                    <!-- Error Message -->
                    <TextBlock x:Name="ErrorTextBlock"
                              Foreground="Red"
                              TextAlignment="Center"
                              Margin="0,10,0,0"
                              Visibility="Collapsed"/>

                </StackPanel>
            </materialDesign:Card>

            <!-- Footer -->
            <StackPanel Grid.Row="2" Orientation="Horizontal"
                       HorizontalAlignment="Center" Margin="20">
                <TextBlock Text="© 2025 نظام إدارة مصنع التمور"
                          FontSize="12"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>

        </Grid>
    </materialDesign:DialogHost>
</Window>
