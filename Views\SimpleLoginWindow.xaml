<Window x:Class="DatesFactoryManager.Views.SimpleLoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام إدارة مصنع التمور (إصدار مبسط)"
        Height="400" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="LightGray">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkBlue" CornerRadius="5" Margin="0,0,0,20">
            <StackPanel Orientation="Vertical" Margin="20">
                <TextBlock Text="🏭" FontSize="48" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="نظام إدارة مصنع التمور"
                          FontSize="20" FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Foreground="White"
                          Margin="0,10,0,0"/>
                <TextBlock Text="تسجيل الدخول (إصدار مبسط)"
                          FontSize="14"
                          HorizontalAlignment="Center"
                          Foreground="LightGray"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Background="White" CornerRadius="5" Padding="30">
            <StackPanel VerticalAlignment="Center">

                <!-- Username -->
                <Label Content="اسم المستخدم:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="UsernameTextBox"
                        Height="35"
                        FontSize="14"
                        Padding="10"
                        Margin="0,0,0,20"
                        BorderBrush="DarkBlue"
                        BorderThickness="2"/>

                <!-- Password -->
                <Label Content="كلمة المرور:" FontWeight="Bold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox"
                            Height="35"
                            FontSize="14"
                            Padding="10"
                            Margin="0,0,0,20"
                            BorderBrush="DarkBlue"
                            BorderThickness="2"/>

                <!-- Remember Me -->
                <CheckBox x:Name="RememberMeCheckBox"
                         Content="تذكرني"
                         Margin="0,0,0,20"
                         HorizontalAlignment="Right"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                       Content="تسجيل الدخول"
                       Height="40"
                       FontSize="16"
                       FontWeight="Bold"
                       Background="DarkBlue"
                       Foreground="White"
                       BorderThickness="0"
                       Click="LoginButton_Click"
                       IsDefault="True"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          TextAlignment="Center"
                          Margin="0,15,0,0"
                          FontWeight="Bold"
                          Visibility="Collapsed"/>

            </StackPanel>
        </Border>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal"
                   HorizontalAlignment="Center" Margin="0,20,0,0">
            <TextBlock Text="© 2025 نظام إدارة مصنع التمور - إصدار مبسط"
                      FontSize="12"
                      Foreground="DarkGray"/>
        </StackPanel>

    </Grid>
</Window>
