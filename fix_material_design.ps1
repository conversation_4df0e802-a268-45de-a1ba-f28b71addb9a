# إصلاح مشاكل Material Design
Write-Host "إصلاح مشاكل Material Design..." -ForegroundColor Green

# قائمة الملفات التي تحتوي على Material Design
$files = @(
    "MainWindow.xaml",
    "Views\LoginWindow.xaml", 
    "Views\DashboardPage.xaml",
    "Views\ProductsPage.xaml",
    "Views\SalesPage.xaml",
    "Views\PurchasesPage.xaml",
    "Views\InventoryPage.xaml",
    "Views\CustomersPage.xaml",
    "Views\SuppliersPage.xaml",
    "Views\ReportsPage.xaml",
    "Views\SettingsPage.xaml"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "معالجة $file..." -ForegroundColor Yellow
        
        # قراءة المحتوى
        $content = Get-Content $file -Raw
        
        # إزالة مراجع Material Design
        $content = $content -replace 'xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"', ''
        $content = $content -replace '<materialDesign:[^>]*>', '<Border>'
        $content = $content -replace '</materialDesign:[^>]*>', '</Border>'
        $content = $content -replace 'materialDesign:[^=]*="[^"]*"', ''
        $content = $content -replace 'Style="\{StaticResource MaterialDesign[^}]*\}"', ''
        
        # حفظ الملف المعدل
        $content | Out-File -FilePath $file -Encoding UTF8
        
        Write-Host "✅ تم إصلاح $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️ الملف $file غير موجود" -ForegroundColor Yellow
    }
}

Write-Host "تم الانتهاء من إصلاح جميع الملفات!" -ForegroundColor Green
