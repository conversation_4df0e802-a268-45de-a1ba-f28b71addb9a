using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class SaleService : BaseDataService<Sale>, ISaleService
    {
        public SaleService(ApplicationDbContext context) : base(context)
        {
        }
        
        public async Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbSet
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.SaleDate >= startDate && s.SaleDate < endDate)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Sale>> GetSalesByCustomerAsync(int customerId)
        {
            return await _dbSet
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.CustomerId == customerId)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }
        
        public async Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _dbSet.Where(s => !s.IsDeleted && s.Status == SaleStatus.مكتملة);
            
            if (startDate.HasValue)
                query = query.Where(s => s.SaleDate >= startDate.Value);
                
            if (endDate.HasValue)
                query = query.Where(s => s.SaleDate < endDate.Value);
            
            return await query.SumAsync(s => s.TotalAmount);
        }
        
        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var today = DateTime.Today;
            var todayString = today.ToString("yyyyMMdd");
            
            var lastInvoice = await _dbSet
                .Where(s => s.InvoiceNumber.StartsWith($"INV-{todayString}"))
                .OrderByDescending(s => s.InvoiceNumber)
                .FirstOrDefaultAsync();
            
            int nextNumber = 1;
            if (lastInvoice != null)
            {
                var lastNumberPart = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }
            
            return $"INV-{todayString}-{nextNumber:D3}";
        }
        
        public async Task<Sale> CreateSaleWithItemsAsync(Sale sale, List<SaleItem> items)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Generate invoice number if not provided
                if (string.IsNullOrEmpty(sale.InvoiceNumber))
                {
                    sale.InvoiceNumber = await GenerateInvoiceNumberAsync();
                }
                
                // Add sale
                var addedSale = await AddAsync(sale);
                
                // Add sale items
                foreach (var item in items)
                {
                    item.SaleId = addedSale.Id;
                    _context.SaleItems.Add(item);
                }
                
                await _context.SaveChangesAsync();
                
                // Update product stock
                var productService = new ProductService(_context);
                foreach (var item in items)
                {
                    await productService.UpdateStockAsync(item.ProductId, (int)item.Quantity, TransactionType.خروج);
                }
                
                await transaction.CommitAsync();
                return addedSale;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        
        public override async Task<Sale?> GetByIdAsync(int id)
        {
            return await _dbSet
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
        }
        
        public override async Task<IEnumerable<Sale>> GetAllAsync()
        {
            return await _dbSet
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Where(s => !s.IsDeleted)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }
    }
}
