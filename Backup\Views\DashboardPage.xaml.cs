using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using DatesFactoryManager.Services;
using LiveCharts;
using LiveCharts.Wpf;

namespace DatesFactoryManager.Views
{
    public partial class DashboardPage : Page
    {
        private readonly IProductService _productService;
        private readonly ISaleService _saleService;
        private readonly ICustomerService _customerService;
        
        public DashboardPage()
        {
            InitializeComponent();
            
            _productService = App.ServiceProvider?.GetRequiredService<IProductService>() 
                             ?? throw new InvalidOperationException("ProductService not found");
            _saleService = App.ServiceProvider?.GetRequiredService<ISaleService>() 
                          ?? throw new InvalidOperationException("SaleService not found");
            _customerService = App.ServiceProvider?.GetRequiredService<ICustomerService>() 
                              ?? throw new InvalidOperationException("CustomerService not found");
            
            Loaded += DashboardPage_Loaded;
        }
        
        private async void DashboardPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDashboardData();
        }
        
        private async Task LoadDashboardData()
        {
            try
            {
                // Load statistics
                await LoadStatistics();
                
                // Load charts
                await LoadSalesChart();
                
                // Load top products
                await LoadTopProducts();
                
                // Load recent activities
                await LoadRecentActivities();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async Task LoadStatistics()
        {
            // Today's sales
            var today = DateTime.Today;
            var todaySales = await _saleService.GetTotalSalesAsync(today, today.AddDays(1));
            TodaySalesTextBlock.Text = $"{todaySales:N0} ريال";
            
            // Total products
            var totalProducts = await _productService.CountAsync();
            TotalProductsTextBlock.Text = totalProducts.ToString();
            
            // Low stock products
            var lowStockProducts = await _productService.GetLowStockProductsAsync();
            LowStockTextBlock.Text = lowStockProducts.Count().ToString();
            
            // Total customers
            var totalCustomers = await _customerService.CountAsync();
            TotalCustomersTextBlock.Text = totalCustomers.ToString();
        }
        
        private async Task LoadSalesChart()
        {
            try
            {
                var salesData = new ChartValues<decimal>();
                var labels = new List<string>();
                
                // Get sales for last 7 days
                for (int i = 6; i >= 0; i--)
                {
                    var date = DateTime.Today.AddDays(-i);
                    var sales = await _saleService.GetTotalSalesAsync(date, date.AddDays(1));
                    salesData.Add(sales);
                    labels.Add(date.ToString("dd/MM"));
                }
                
                SalesChart.Series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "المبيعات",
                        Values = salesData,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8
                    }
                };
                
                SalesChart.AxisX[0].Labels = labels;
            }
            catch (Exception ex)
            {
                // Handle chart loading error silently or show a simple message
                Console.WriteLine($"Error loading sales chart: {ex.Message}");
            }
        }
        
        private async Task LoadTopProducts()
        {
            try
            {
                // This would require a more complex query to get top selling products
                // For now, we'll show a placeholder
                var topProducts = new List<TopProductItem>
                {
                    new TopProductItem { Name = "تمور مجهول كبير", Quantity = "150 كيلو" },
                    new TopProductItem { Name = "تمور صقعي متوسط", Quantity = "120 كيلو" },
                    new TopProductItem { Name = "تمور خلاص صغير", Quantity = "95 كيلو" },
                    new TopProductItem { Name = "تمور زهدي مخلوط", Quantity = "80 كيلو" },
                    new TopProductItem { Name = "تمور حلاوي كبير", Quantity = "65 كيلو" }
                };
                
                TopProductsListView.ItemsSource = topProducts;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading top products: {ex.Message}");
            }
        }
        
        private async Task LoadRecentActivities()
        {
            try
            {
                // This would require a more complex query to get recent activities
                // For now, we'll show a placeholder
                var recentActivities = new List<RecentActivityItem>
                {
                    new RecentActivityItem 
                    { 
                        Type = "مبيعات", 
                        Description = "فاتورة رقم INV-001", 
                        Amount = "1,250 ريال", 
                        Date = DateTime.Now.ToString("dd/MM/yyyy HH:mm") 
                    },
                    new RecentActivityItem 
                    { 
                        Type = "مشتريات", 
                        Description = "فاتورة شراء رقم PUR-001", 
                        Amount = "2,500 ريال", 
                        Date = DateTime.Now.AddHours(-2).ToString("dd/MM/yyyy HH:mm") 
                    },
                    new RecentActivityItem 
                    { 
                        Type = "مخزون", 
                        Description = "إضافة مخزون تمور مجهول", 
                        Amount = "500 كيلو", 
                        Date = DateTime.Now.AddHours(-4).ToString("dd/MM/yyyy HH:mm") 
                    }
                };
                
                RecentActivitiesDataGrid.ItemsSource = recentActivities;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading recent activities: {ex.Message}");
            }
        }
    }
    
    public class TopProductItem
    {
        public string Name { get; set; } = string.Empty;
        public string Quantity { get; set; } = string.Empty;
    }
    
    public class RecentActivityItem
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
    }
}
