@echo off
echo ========================================
echo إصلاح المشروع الأصلي بالكامل
echo ========================================
echo.

echo 1. إنشاء نسخ احتياطية...
mkdir Backup 2>nul
copy *.xaml Backup\ >nul 2>&1
copy *.cs Backup\ >nul 2>&1
copy *.csproj Backup\ >nul 2>&1
xcopy /E /I /Y Views Backup\Views\ >nul 2>&1

echo 2. تنظيف المشروع...
dotnet clean >nul 2>&1
rmdir /s /q bin >nul 2>&1
rmdir /s /q obj >nul 2>&1

echo 3. إزالة Material Design من csproj...
powershell -Command "(Get-Content DatesFactoryManager.csproj) -replace '.*MaterialDesignThemes.*', '' -replace '.*LiveCharts.*', '' | Set-Content DatesFactoryManager.csproj"

echo 4. إضافة ModernWpf...
dotnet add package ModernWpfUI --version 0.9.6

echo 5. استعادة الحزم...
dotnet restore

echo تم الانتهاء من التحضير الأولي!
echo الآن سيتم إصلاح ملفات XAML...

pause
