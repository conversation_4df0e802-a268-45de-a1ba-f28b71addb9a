using System.ComponentModel.DataAnnotations;

namespace DatesFactoryManager.Models
{
    public class Purchase : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime PurchaseDate { get; set; } = DateTime.Now;
        
        [Required]
        public int SupplierId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal SubTotal { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Range(0, double.MaxValue)]
        public decimal TaxAmount { get; set; } = 0;
        
        [Range(0, 100)]
        public decimal TaxPercentage { get; set; } = 15;
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal TotalAmount { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal PaidAmount { get; set; } = 0;
        
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.نقدي;
        
        public PurchaseStatus Status { get; set; } = PurchaseStatus.مكتملة;
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        // Navigation properties
        public virtual Supplier Supplier { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual ICollection<PurchaseItem> PurchaseItems { get; set; } = new List<PurchaseItem>();
    }
    
    public class PurchaseItem : BaseEntity
    {
        [Required]
        public int PurchaseId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; } = 0;
        
        public decimal TotalPrice => (Quantity * UnitPrice) * (1 - DiscountPercentage / 100);
        
        // Navigation properties
        public virtual Purchase Purchase { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }
    
    public enum PurchaseStatus
    {
        مسودة = 1,
        مكتملة = 2,
        ملغية = 3,
        مرتجعة = 4
    }
}
