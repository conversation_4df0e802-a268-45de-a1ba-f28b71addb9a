using System.Windows; 
 
namespace TestApp 
{ 
    public partial class MainWindow : Window 
    { 
        public MainWindow() 
        { 
            InitializeComponent(); 
            MessageBox.Show("تم تحميل التطبيق بنجاح!", "نجح!", MessageBoxButton.OK, MessageBoxImage.Information); 
        } 
 
        private void CloseButton_Click(object sender, RoutedEventArgs e) 
        { 
            MessageBox.Show("شكراً! سيتم إغلاق التطبيق.", "إغلاق", MessageBoxButton.OK, MessageBoxImage.Information); 
            this.Close(); 
        } 
    } 
} 
