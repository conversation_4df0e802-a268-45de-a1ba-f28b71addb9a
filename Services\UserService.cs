using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class UserService : BaseDataService<User>, IUserService
    {
        public UserService(ApplicationDbContext context) : base(context)
        {
        }
        
        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            var user = await _dbSet
                .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted && u.IsActive);
                
            if (user == null || !BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                return null;
                
            user.LastLoginAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return user;
        }
        
        public async Task<User?> GetByUsernameAsync(string username)
        {
            return await _dbSet
                .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);
        }
        
        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await GetByIdAsync(userId);
            if (user == null || !BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
                return false;
                
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            await UpdateAsync(user);
            return true;
        }
        
        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await _dbSet
                .Where(u => !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }
        
        public override async Task<User> AddAsync(User entity)
        {
            // Hash password before saving
            if (!string.IsNullOrEmpty(entity.PasswordHash))
            {
                entity.PasswordHash = BCrypt.Net.BCrypt.HashPassword(entity.PasswordHash);
            }
            
            return await base.AddAsync(entity);
        }
    }
}
