@echo off
echo ===================================
echo تشغيل الإصدار المبسط من التطبيق
echo ===================================
echo.

REM نسخ احتياطية من الملفات الأصلية
echo إنشاء نسخ احتياطية...
copy App.xaml App.xaml.backup >nul 2>&1

REM استبدال الملفات بالإصدار المبسط
echo استبدال الملفات بالإصدار المبسط...
copy SimpleApp.xaml App.xaml >nul 2>&1

echo.
echo بناء الإصدار المبسط...
dotnet build --no-restore

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم البناء بنجاح!
    echo.
    echo تشغيل التطبيق المبسط...
    echo إذا ظهرت رسائل تأكيد، فهذا يعني أن التطبيق يعمل!
    echo.
    
    REM تشغيل التطبيق
    start "نظام إدارة مصنع التمور - إصدار مبسط" "bin\Debug\net8.0-windows\DatesFactoryManager.exe"
    
    echo تم تشغيل التطبيق المبسط!
    echo.
    echo إذا ظهرت النوافذ، فالمشكلة كانت في Material Design
    echo إذا لم تظهر، فالمشكلة أعمق من ذلك
    echo.
) else (
    echo ❌ فشل في البناء!
    echo تحقق من الأخطاء أعلاه
)

echo.
echo استعادة الملفات الأصلية...
copy App.xaml.backup App.xaml >nul 2>&1
del App.xaml.backup >nul 2>&1

echo.
pause
