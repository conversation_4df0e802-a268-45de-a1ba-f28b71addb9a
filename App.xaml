﻿<Application x:Class="DatesFactoryManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DatesFactoryManager"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Styles -->
            <Style TargetType="Window">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>

            <Style TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="TextAlignment" Value="Right" />
            </Style>

            <Style TargetType="Label">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="HorizontalContentAlignment" Value="Right" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
