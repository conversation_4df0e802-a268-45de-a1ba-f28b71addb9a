﻿<Application x:Class="DatesFactoryManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DatesFactoryManager"

             StartupUri="Views/SimpleLoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>


            <!-- Global Styles -->
            <Style TargetType="Window">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>

            <Style TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="TextAlignment" Value="Right" />
            </Style>

            <Style TargetType="Label">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="HorizontalContentAlignment" Value="Right" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
