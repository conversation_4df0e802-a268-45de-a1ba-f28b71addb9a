using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class ProductService : BaseDataService<Product>, IProductService
    {
        public ProductService(ApplicationDbContext context) : base(context)
        {
        }
        
        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _dbSet
                .Where(p => !p.IsDeleted && p.IsActive && p.StockQuantity <= p.MinimumStock)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Product>> GetByCodeAsync(string code)
        {
            return await _dbSet
                .Where(p => !p.IsDeleted && p.Code.Contains(code))
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Product>> GetByCategoryAsync(ProductCategory category)
        {
            return await _dbSet
                .Where(p => !p.IsDeleted && p.IsActive && p.Category == category)
                .ToListAsync();
        }
        
        public async Task<bool> UpdateStockAsync(int productId, int quantity, TransactionType transactionType)
        {
            var product = await GetByIdAsync(productId);
            if (product == null) return false;
            
            switch (transactionType)
            {
                case TransactionType.دخول:
                    product.StockQuantity += quantity;
                    break;
                case TransactionType.خروج:
                    if (product.StockQuantity < quantity) return false;
                    product.StockQuantity -= quantity;
                    break;
                case TransactionType.تعديل:
                    product.StockQuantity = quantity;
                    break;
                case TransactionType.تالف:
                    if (product.StockQuantity < quantity) return false;
                    product.StockQuantity -= quantity;
                    break;
                case TransactionType.إرجاع:
                    product.StockQuantity += quantity;
                    break;
            }
            
            await UpdateAsync(product);
            return true;
        }
        
        public override async Task<IEnumerable<Product>> GetAllAsync()
        {
            return await _dbSet
                .Where(p => !p.IsDeleted)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }
    }
}
