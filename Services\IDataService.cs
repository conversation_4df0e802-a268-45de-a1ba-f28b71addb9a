using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public interface IDataService<T> where T : BaseEntity
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<T?> GetByIdAsync(int id);
        Task<T> AddAsync(T entity);
        Task<T> UpdateAsync(T entity);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<int> CountAsync();
    }
    
    public interface IProductService : IDataService<Product>
    {
        Task<IEnumerable<Product>> GetLowStockProductsAsync();
        Task<IEnumerable<Product>> GetByCodeAsync(string code);
        Task<IEnumerable<Product>> GetByCategoryAsync(ProductCategory category);
        Task<bool> UpdateStockAsync(int productId, int quantity, TransactionType transactionType);
    }
    
    public interface ICustomerService : IDataService<Customer>
    {
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
        Task<Customer?> GetByPhoneAsync(string phone);
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task<bool> UpdateBalanceAsync(int customerId, decimal amount);
    }
    
    public interface ISupplierService : IDataService<Supplier>
    {
        Task<IEnumerable<Supplier>> GetActiveSuppliersAsync();
        Task<Supplier?> GetByPhoneAsync(string phone);
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task<bool> UpdateBalanceAsync(int supplierId, decimal amount);
    }
    
    public interface ISaleService : IDataService<Sale>
    {
        Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Sale>> GetSalesByCustomerAsync(int customerId);
        Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<string> GenerateInvoiceNumberAsync();
        Task<Sale> CreateSaleWithItemsAsync(Sale sale, List<SaleItem> items);
    }
    
    public interface IPurchaseService : IDataService<Purchase>
    {
        Task<IEnumerable<Purchase>> GetPurchasesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Purchase>> GetPurchasesBySupplierAsync(int supplierId);
        Task<decimal> GetTotalPurchasesAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<string> GenerateInvoiceNumberAsync();
        Task<Purchase> CreatePurchaseWithItemsAsync(Purchase purchase, List<PurchaseItem> items);
    }
    
    public interface IUserService : IDataService<User>
    {
        Task<User?> AuthenticateAsync(string username, string password);
        Task<User?> GetByUsernameAsync(string username);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<IEnumerable<User>> GetActiveUsersAsync();
    }
    
    public interface IInventoryService
    {
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByProductAsync(int productId);
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<InventoryTransaction> AddTransactionAsync(InventoryTransaction transaction);
        Task<decimal> GetProductCurrentStockAsync(int productId);
        Task<decimal> GetProductStockValueAsync(int productId);
    }
}
