@echo off
echo ========================================
echo التشخيص النهائي لمشكلة التطبيق
echo ========================================
echo.

echo 1. فحص ملفات التطبيق...
if exist "bin\Debug\net8.0-windows\DatesFactoryManager.exe" (
    echo ✅ الملف التنفيذي موجود
) else (
    echo ❌ الملف التنفيذي غير موجود
    goto :end
)

echo.
echo 2. فحص التبعيات...
if exist "bin\Debug\net8.0-windows\DatesFactoryManager.dll" (
    echo ✅ ملف DLL الرئيسي موجود
) else (
    echo ❌ ملف DLL الرئيسي غير موجود
)

echo.
echo 3. محاولة تشغيل مع معلومات مفصلة...
echo سيتم تشغيل التطبيق مع إظهار أي أخطاء...
echo.

REM تشغيل التطبيق مع التقاط الأخطاء
echo تشغيل التطبيق...
cd bin\Debug\net8.0-windows\
DatesFactoryManager.exe 2>&1
set EXIT_CODE=%ERRORLEVEL%

cd ..\..\..

echo.
echo رمز الخروج: %EXIT_CODE%

if %EXIT_CODE% EQU 0 (
    echo ✅ التطبيق عمل بدون أخطاء
) else (
    echo ❌ التطبيق خرج برمز خطأ: %EXIT_CODE%
)

echo.
echo 4. فحص العمليات الجارية...
tasklist | findstr /i "DatesFactory" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ التطبيق يعمل في الخلفية
    echo قائمة العمليات:
    tasklist | findstr /i "DatesFactory"
) else (
    echo ❌ التطبيق غير موجود في العمليات الجارية
)

echo.
echo 5. التوصيات النهائية:
echo.
echo إذا كان التطبيق يعمل ولكن لا تظهر النوافذ:
echo - اضغط Alt+Tab للتنقل بين النوافذ
echo - تحقق من شريط المهام
echo - قد تكون النافذة خلف نوافذ أخرى
echo - جرب تغيير دقة الشاشة مؤقتاً
echo.
echo إذا كان التطبيق لا يعمل نهائياً:
echo - تحقق من Windows Defender
echo - شغل كمدير (Run as Administrator)
echo - أعد تشغيل الكمبيوتر
echo.

:end
echo.
pause
