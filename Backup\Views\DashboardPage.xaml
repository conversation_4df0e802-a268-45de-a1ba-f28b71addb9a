<Page x:Class="DatesFactoryManager.Views.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:DatesFactoryManager.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="لوحة التحكم">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Page Title -->
            <TextBlock Grid.Row="0" Text="لوحة التحكم"
                      FontSize="24" FontWeight="Bold"
                      Margin="0,0,0,20"/>

            <!-- Statistics Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Today Sales -->
                <materialDesign:Card Grid.Column="0" Margin="0,0,10,0">
                    <Grid Height="120">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" VerticalAlignment="Center"
                                   HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="CashRegister"
                                                   Width="40" Height="40"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock x:Name="TodaySalesTextBlock"
                                      Text="0 ريال"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Margin="0,5,0,0"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1" Text="مبيعات اليوم"
                                  FontSize="14"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,10"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </Grid>
                </materialDesign:Card>

                <!-- Total Products -->
                <materialDesign:Card Grid.Column="1" Margin="5,0,5,0">
                    <Grid Height="120">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" VerticalAlignment="Center"
                                   HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Package"
                                                   Width="40" Height="40"
                                                   Foreground="Green"/>
                            <TextBlock x:Name="TotalProductsTextBlock"
                                      Text="0"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Margin="0,5,0,0"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1" Text="إجمالي الأصناف"
                                  FontSize="14"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,10"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </Grid>
                </materialDesign:Card>

                <!-- Low Stock -->
                <materialDesign:Card Grid.Column="2" Margin="5,0,5,0">
                    <Grid Height="120">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" VerticalAlignment="Center"
                                   HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                   Width="40" Height="40"
                                                   Foreground="Orange"/>
                            <TextBlock x:Name="LowStockTextBlock"
                                      Text="0"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Margin="0,5,0,0"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1" Text="أصناف منخفضة المخزون"
                                  FontSize="14"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,10"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </Grid>
                </materialDesign:Card>

                <!-- Total Customers -->
                <materialDesign:Card Grid.Column="3" Margin="10,0,0,0">
                    <Grid Height="120">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" VerticalAlignment="Center"
                                   HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="AccountGroup"
                                                   Width="40" Height="40"
                                                   Foreground="Blue"/>
                            <TextBlock x:Name="TotalCustomersTextBlock"
                                      Text="0"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Margin="0,5,0,0"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1" Text="إجمالي العملاء"
                                  FontSize="14"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,10"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Charts Section -->
            <Grid Grid.Row="2" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sales Chart -->
                <materialDesign:Card Grid.Column="0" Margin="0,0,10,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="300"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="مبيعات آخر 7 أيام"
                                  FontSize="16" FontWeight="Bold"
                                  Margin="20,20,20,10"/>

                        <lvc:CartesianChart Grid.Row="1" x:Name="SalesChart"
                                          Margin="20,0,20,20">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="التاريخ" FontSize="12"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="المبلغ (ريال)" FontSize="12"/>
                            </lvc:CartesianChart.AxisY>
                        </lvc:CartesianChart>
                    </Grid>
                </materialDesign:Card>

                <!-- Top Products -->
                <materialDesign:Card Grid.Column="1" Margin="10,0,0,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="أكثر الأصناف مبيعاً"
                                  FontSize="16" FontWeight="Bold"
                                  Margin="20,20,20,10"/>

                        <ListView Grid.Row="1" x:Name="TopProductsListView"
                                 Margin="20,0,20,20">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                  Text="{Binding Name}"
                                                  FontSize="14"/>
                                        <TextBlock Grid.Column="1"
                                                  Text="{Binding Quantity}"
                                                  FontSize="14" FontWeight="Bold"
                                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                    </Grid>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Recent Activities -->
            <materialDesign:Card Grid.Row="3">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="آخر العمليات"
                              FontSize="16" FontWeight="Bold"
                              Margin="20,20,20,10"/>

                    <DataGrid Grid.Row="1" x:Name="RecentActivitiesDataGrid"
                             Margin="20,0,20,20"
                             AutoGenerateColumns="False"
                             IsReadOnly="True"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

        </Grid>
    </ScrollViewer>
</Page>
