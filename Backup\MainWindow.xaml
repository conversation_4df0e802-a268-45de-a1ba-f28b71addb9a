﻿<Window x:Class="DatesFactoryManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DatesFactoryManager"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="نظام إدارة مصنع التمور"
        Height="800" Width="1200"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}">

    <materialDesign:DialogHost>
        <DockPanel>

            <!-- Top Menu Bar -->
            <materialDesign:Card DockPanel.Dock="Top"
                                Margin="0,0,0,5">
                <Grid Height="60">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Menu Button -->
                    <Button x:Name="MenuButton"
                           Grid.Column="0"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Margin="10"
                           Click="MenuButton_Click">
                        <materialDesign:PackIcon Kind="Menu" Width="24" Height="24"/>
                    </Button>

                    <!-- Title -->
                    <StackPanel Grid.Column="1"
                               Orientation="Horizontal"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Factory"
                                               Width="32" Height="32"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="نظام إدارة مصنع التمور"
                                  FontSize="20" FontWeight="Bold"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- User Info -->
                    <StackPanel Grid.Column="2"
                               Orientation="Horizontal"
                               VerticalAlignment="Center"
                               Margin="10">
                        <TextBlock x:Name="UserNameTextBlock"
                                  Text="مرحباً، المستخدم"
                                  VerticalAlignment="Center"
                                  Margin="0,0,10,0"/>
                        <Button x:Name="LogoutButton"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Click="LogoutButton_Click"
                               ToolTip="تسجيل الخروج">
                            <materialDesign:PackIcon Kind="Logout" Width="20" Height="20"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Side Navigation -->
            <materialDesign:Card x:Name="SideNavCard"
                                DockPanel.Dock="Right"
                                Width="250"
                                Margin="5,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">

                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Dashboard">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="لوحة التحكم"/>
                            </StackPanel>
                        </Button>

                        <!-- Products -->
                        <Button x:Name="ProductsButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Products">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Package"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="الأصناف"/>
                            </StackPanel>
                        </Button>

                        <!-- Sales -->
                        <Button x:Name="SalesButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Sales">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashRegister"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="المبيعات"/>
                            </StackPanel>
                        </Button>

                        <!-- Purchases -->
                        <Button x:Name="PurchasesButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Purchases">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ShoppingCart"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="المشتريات"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory -->
                        <Button x:Name="InventoryButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Inventory">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Warehouse"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="المخزون"/>
                            </StackPanel>
                        </Button>

                        <!-- Customers -->
                        <Button x:Name="CustomersButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Customers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="العملاء"/>
                            </StackPanel>
                        </Button>

                        <!-- Suppliers -->
                        <Button x:Name="SuppliersButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Suppliers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TruckDelivery"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="الموردين"/>
                            </StackPanel>
                        </Button>

                        <!-- Reports -->
                        <Button x:Name="ReportsButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Reports">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="التقارير"/>
                            </StackPanel>
                        </Button>

                        <!-- Settings -->
                        <Button x:Name="SettingsButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Height="50" Margin="0,5"
                               Click="NavigationButton_Click"
                               Tag="Settings">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings"
                                                       Width="20" Height="20"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="الإعدادات"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Main Content Area -->
            <materialDesign:Card DockPanel.Dock="Left"
                                Margin="0,0,5,0">
                <Frame x:Name="MainFrame"
                      NavigationUIVisibility="Hidden"
                      Background="Transparent"/>
            </materialDesign:Card>

        </DockPanel>
    </materialDesign:DialogHost>
</Window>
