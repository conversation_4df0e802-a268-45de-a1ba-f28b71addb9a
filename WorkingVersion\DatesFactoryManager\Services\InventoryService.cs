using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly ApplicationDbContext _context;
        
        public InventoryService(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByProductAsync(int productId)
        {
            return await _context.InventoryTransactions
                .Include(it => it.Product)
                .Include(it => it.User)
                .Where(it => !it.IsDeleted && it.ProductId == productId)
                .OrderByDescending(it => it.CreatedAt)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.InventoryTransactions
                .Include(it => it.Product)
                .Include(it => it.User)
                .Where(it => !it.IsDeleted && it.CreatedAt >= startDate && it.CreatedAt < endDate)
                .OrderByDescending(it => it.CreatedAt)
                .ToListAsync();
        }
        
        public async Task<InventoryTransaction> AddTransactionAsync(InventoryTransaction transaction)
        {
            transaction.CreatedAt = DateTime.Now;
            transaction.IsSynced = false;
            
            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();
            
            return transaction;
        }
        
        public async Task<decimal> GetProductCurrentStockAsync(int productId)
        {
            var product = await _context.Products.FindAsync(productId);
            return product?.StockQuantity ?? 0;
        }
        
        public async Task<decimal> GetProductStockValueAsync(int productId)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null) return 0;
            
            return product.StockQuantity * product.PurchasePrice;
        }
    }
}
