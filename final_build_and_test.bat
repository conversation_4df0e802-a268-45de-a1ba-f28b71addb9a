@echo off
echo ========================================
echo البناء النهائي واختبار التطبيق المُحدث
echo ========================================
echo.

echo 1. تنظيف المشروع...
dotnet clean DatesFactoryManager.csproj >nul 2>&1

echo 2. استعادة الحزم...
dotnet restore DatesFactoryManager.csproj

echo 3. بناء المشروع...
dotnet build DatesFactoryManager.csproj

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم البناء بنجاح!
    echo.
    echo 🎉 تم إصلاح التطبيق بنجاح!
    echo.
    echo التحسينات المضافة:
    echo ✅ إزالة Material Design المُشكل
    echo ✅ إضافة ModernWpf كبديل متوافق
    echo ✅ واجهة مستخدم حديثة وجميلة
    echo ✅ نوافذ وصفحات محدثة
    echo ✅ أيقونات وألوان متناسقة
    echo.
    echo تشغيل التطبيق المُحدث...
    start "نظام إدارة مصنع التمور - ModernWpf" "bin\Debug\net8.0-windows\DatesFactoryManager.exe"
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo 🎊 مبروك! التطبيق يعمل الآن بواجهة حديثة!
) else (
    echo ❌ فشل في البناء!
    echo تحقق من الأخطاء أعلاه
    echo.
    echo يمكنك استخدام الإصدار البديل في مجلد WorkingVersion
)

echo.
pause
