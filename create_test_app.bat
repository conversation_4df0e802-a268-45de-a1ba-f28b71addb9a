@echo off
echo ========================================
echo إنشاء تطبيق اختبار بسيط
echo ========================================
echo.

REM إنشاء مجلد جديد للاختبار
echo إنشاء مجلد الاختبار...
mkdir SimpleWPFTest 2>nul
cd SimpleWPFTest

REM إنشاء تطبيق WPF جديد
echo إنشاء تطبيق WPF جديد...
dotnet new wpf -n TestApp --force

cd TestApp

REM تعديل MainWindow.xaml
echo تعديل النافذة الرئيسية...
echo ^<Window x:Class="TestApp.MainWindow" > MainWindow.xaml
echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" >> MainWindow.xaml
echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" >> MainWindow.xaml
echo         Title="اختبار WPF - يعمل!" Height="400" Width="500" >> MainWindow.xaml
echo         WindowStartupLocation="CenterScreen" >> MainWindow.xaml
echo         Background="LightBlue"^> >> MainWindow.xaml
echo     ^<Grid^> >> MainWindow.xaml
echo         ^<StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"^> >> MainWindow.xaml
echo             ^<TextBlock Text="مرحباً! WPF يعمل بنجاح!" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="20"/^> >> MainWindow.xaml
echo             ^<TextBlock Text="إذا ظهرت هذه النافذة، فإن WPF يعمل على نظامك" FontSize="14" HorizontalAlignment="Center" Margin="10"/^> >> MainWindow.xaml
echo             ^<Button Content="إغلاق" Width="100" Height="35" Margin="20" Click="CloseButton_Click"/^> >> MainWindow.xaml
echo         ^</StackPanel^> >> MainWindow.xaml
echo     ^</Grid^> >> MainWindow.xaml
echo ^</Window^> >> MainWindow.xaml

REM تعديل MainWindow.xaml.cs
echo تعديل كود النافذة...
echo using System.Windows; > MainWindow.xaml.cs
echo. >> MainWindow.xaml.cs
echo namespace TestApp >> MainWindow.xaml.cs
echo { >> MainWindow.xaml.cs
echo     public partial class MainWindow : Window >> MainWindow.xaml.cs
echo     { >> MainWindow.xaml.cs
echo         public MainWindow() >> MainWindow.xaml.cs
echo         { >> MainWindow.xaml.cs
echo             InitializeComponent(); >> MainWindow.xaml.cs
echo             MessageBox.Show("تم تحميل التطبيق بنجاح!", "نجح!", MessageBoxButton.OK, MessageBoxImage.Information); >> MainWindow.xaml.cs
echo         } >> MainWindow.xaml.cs
echo. >> MainWindow.xaml.cs
echo         private void CloseButton_Click(object sender, RoutedEventArgs e) >> MainWindow.xaml.cs
echo         { >> MainWindow.xaml.cs
echo             MessageBox.Show("شكراً! سيتم إغلاق التطبيق.", "إغلاق", MessageBoxButton.OK, MessageBoxImage.Information); >> MainWindow.xaml.cs
echo             this.Close(); >> MainWindow.xaml.cs
echo         } >> MainWindow.xaml.cs
echo     } >> MainWindow.xaml.cs
echo } >> MainWindow.xaml.cs

echo.
echo بناء التطبيق...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم البناء بنجاح!
    echo.
    echo تشغيل التطبيق...
    start "اختبار WPF" "bin\Debug\net8.0-windows\TestApp.exe"
    echo.
    echo إذا ظهرت النافذة، فإن WPF يعمل على نظامك!
    echo إذا لم تظهر، فهناك مشكلة في النظام نفسه.
) else (
    echo ❌ فشل في البناء!
)

echo.
echo العودة للمجلد الأصلي...
cd ..\..

pause
