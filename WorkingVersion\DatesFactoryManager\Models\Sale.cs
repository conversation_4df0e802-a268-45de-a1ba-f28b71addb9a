using System.ComponentModel.DataAnnotations;

namespace DatesFactoryManager.Models
{
    public class Sale : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime SaleDate { get; set; } = DateTime.Now;
        
        [Required]
        public int CustomerId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal SubTotal { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; } = 0;
        
        [Range(0, double.MaxValue)]
        public decimal TaxAmount { get; set; } = 0;
        
        [Range(0, 100)]
        public decimal TaxPercentage { get; set; } = 15; // VAT in Saudi Arabia
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal TotalAmount { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal PaidAmount { get; set; } = 0;
        
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.نقدي;
        
        public SaleStatus Status { get; set; } = SaleStatus.مكتملة;
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
    }
    
    public class SaleItem : BaseEntity
    {
        [Required]
        public int SaleId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; } = 0;
        
        public decimal TotalPrice => (Quantity * UnitPrice) * (1 - DiscountPercentage / 100);
        
        // Navigation properties
        public virtual Sale Sale { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }
    
    public enum PaymentMethod
    {
        نقدي = 1,
        بطاقة = 2,
        تحويل = 3,
        آجل = 4,
        مختلط = 5
    }
    
    public enum SaleStatus
    {
        مسودة = 1,
        مكتملة = 2,
        ملغية = 3,
        مرتجعة = 4
    }
}
