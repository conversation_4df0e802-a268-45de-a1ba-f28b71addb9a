using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class SupplierService : BaseDataService<Supplier>, ISupplierService
    {
        public SupplierService(ApplicationDbContext context) : base(context)
        {
        }
        
        public async Task<IEnumerable<Supplier>> GetActiveSuppliersAsync()
        {
            return await _dbSet
                .Where(s => !s.IsDeleted && s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }
        
        public async Task<Supplier?> GetByPhoneAsync(string phone)
        {
            return await _dbSet
                .FirstOrDefaultAsync(s => s.Phone == phone && !s.IsDeleted);
        }
        
        public async Task<decimal> GetSupplierBalanceAsync(int supplierId)
        {
            var supplier = await GetByIdAsync(supplierId);
            return supplier?.CurrentBalance ?? 0;
        }
        
        public async Task<bool> UpdateBalanceAsync(int supplierId, decimal amount)
        {
            var supplier = await GetByIdAsync(supplierId);
            if (supplier == null) return false;
            
            supplier.CurrentBalance += amount;
            await UpdateAsync(supplier);
            return true;
        }
        
        public override async Task<IEnumerable<Supplier>> GetAllAsync()
        {
            return await _dbSet
                .Where(s => !s.IsDeleted)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }
    }
}
