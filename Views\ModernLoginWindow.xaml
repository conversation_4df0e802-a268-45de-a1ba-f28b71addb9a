<Window x:Class="DatesFactoryManager.Views.ModernLoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="http://schemas.modernwpf.com/2019"
        Title="تسجيل الدخول - نظام إدارة مصنع التمور"
        Height="500" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        ui:WindowHelper.UseModernWindowStyle="True">

    <Grid Background="{DynamicResource SystemControlPageBackgroundChromeLowBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="{DynamicResource SystemControlHighlightAccentBrush}" 
                CornerRadius="0,0,8,8" 
                Margin="20,20,20,10">
            <StackPanel Orientation="Vertical" Margin="20">
                <ui:FontIcon Glyph="&#xE7F4;" 
                           FontSize="48" 
                           HorizontalAlignment="Center" 
                           Foreground="White"/>
                <TextBlock Text="نظام إدارة مصنع التمور"
                          FontSize="22" FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Foreground="White"
                          Margin="0,15,0,0"/>
                <TextBlock Text="تسجيل الدخول"
                          FontSize="16"
                          HorizontalAlignment="Center"
                          Foreground="White"
                          Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <ui:SimpleStackPanel Grid.Row="1" 
                           Spacing="20" 
                           Margin="40,20,40,20"
                           VerticalAlignment="Center">

            <!-- Username -->
            <StackPanel>
                <TextBlock Text="اسم المستخدم" 
                          FontWeight="SemiBold" 
                          Margin="0,0,0,8"/>
                <TextBox x:Name="UsernameTextBox"
                        Height="40"
                        FontSize="14"
                        ui:ControlHelper.PlaceholderText="أدخل اسم المستخدم"/>
            </StackPanel>

            <!-- Password -->
            <StackPanel>
                <TextBlock Text="كلمة المرور" 
                          FontWeight="SemiBold" 
                          Margin="0,0,0,8"/>
                <PasswordBox x:Name="PasswordBox"
                            Height="40"
                            FontSize="14"
                            ui:ControlHelper.PlaceholderText="أدخل كلمة المرور"/>
            </StackPanel>

            <!-- Remember Me -->
            <CheckBox x:Name="RememberMeCheckBox"
                     Content="تذكرني"
                     HorizontalAlignment="Right"/>

            <!-- Login Button -->
            <Button x:Name="LoginButton"
                   Content="تسجيل الدخول"
                   Height="44"
                   FontSize="16"
                   FontWeight="SemiBold"
                   Style="{StaticResource AccentButtonStyle}"
                   Click="LoginButton_Click"
                   IsDefault="True"/>

            <!-- Error Message -->
            <ui:InfoBar x:Name="ErrorInfoBar"
                       IsOpen="False"
                       Severity="Error"
                       Title="خطأ في تسجيل الدخول"
                       Message=""
                       IsClosable="True"/>

        </ui:SimpleStackPanel>

        <!-- Footer -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal"
                   HorizontalAlignment="Center" 
                   Margin="20">
            <ui:FontIcon Glyph="&#xE946;" 
                       FontSize="12" 
                       Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                       Margin="0,0,5,0"/>
            <TextBlock Text="© 2025 نظام إدارة مصنع التمور"
                      FontSize="12"
                      Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
        </StackPanel>

    </Grid>
</Window>
