{"format": 1, "restore": {"D:\\New folder (2)\\تطبيق كمبيوتر\\DatesFactoryManager\\WorkingVersion\\DatesFactoryManager\\DatesFactoryManager.csproj": {}}, "projects": {"D:\\New folder (2)\\تطبيق كمبيوتر\\DatesFactoryManager\\WorkingVersion\\DatesFactoryManager\\DatesFactoryManager.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\New folder (2)\\تطبيق كمبيوتر\\DatesFactoryManager\\WorkingVersion\\DatesFactoryManager\\DatesFactoryManager.csproj", "projectName": "DatesFactoryManager", "projectPath": "D:\\New folder (2)\\تطبيق كمبيوتر\\DatesFactoryManager\\WorkingVersion\\DatesFactoryManager\\DatesFactoryManager.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\New folder (2)\\تطبيق كمبيوتر\\DatesFactoryManager\\WorkingVersion\\DatesFactoryManager\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}}}