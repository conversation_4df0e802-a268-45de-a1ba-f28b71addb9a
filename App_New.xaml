<Application x:Class="DatesFactoryManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DatesFactoryManager"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             StartupUri="Views/SimpleLoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemeResources />
                <ui:XamlControlsResources />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Styles -->
            <Style TargetType="Window">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="ui:WindowHelper.UseModernWindowStyle" Value="True" />
            </Style>

            <Style TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="TextAlignment" Value="Right" />
            </Style>

            <Style TargetType="Label">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="HorizontalContentAlignment" Value="Right" />
            </Style>

            <!-- Modern Button Style -->
            <Style TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Padding" Value="16,8" />
            </Style>

            <!-- Modern TextBox Style -->
            <Style TargetType="TextBox" BasedOn="{StaticResource DefaultTextBoxStyle}">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Padding" Value="12,8" />
            </Style>

            <!-- Modern PasswordBox Style -->
            <Style TargetType="PasswordBox" BasedOn="{StaticResource DefaultPasswordBoxStyle}">
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Padding" Value="12,8" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
