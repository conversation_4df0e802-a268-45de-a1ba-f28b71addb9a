@echo off
echo ========================================
echo إنشاء مشروع نظيف جديد
echo ========================================
echo.

echo 1. إنشاء مجلد المشروع الجديد...
mkdir CleanDatesFactory 2>nul
cd CleanDatesFactory

echo 2. إنشاء مشروع WPF جديد...
dotnet new wpf -n DatesFactoryManager --force

cd DatesFactoryManager

echo 3. إضافة الحزم المطلوبة...
dotnet add package BCrypt.Net-Next --version 4.0.3
dotnet add package Microsoft.EntityFrameworkCore.Sqlite --version 9.0.6
dotnet add package Microsoft.Extensions.DependencyInjection --version 9.0.6
dotnet add package ModernWpfUI --version 0.9.6

echo 4. نسخ الملفات الأساسية من المشروع الأصلي...
xcopy /E /I /Y "..\..\Data" "Data\" >nul 2>&1
xcopy /E /I /Y "..\..\Models" "Models\" >nul 2>&1
xcopy /E /I /Y "..\..\Services" "Services\" >nul 2>&1
xcopy /E /I /Y "..\..\Helpers" "Helpers\" >nul 2>&1

echo 5. نسخ الواجهات الحديثة...
mkdir Views 2>nul
copy "..\..\Views\Modern*.xaml" "Views\" >nul 2>&1
copy "..\..\Views\Modern*.xaml.cs" "Views\" >nul 2>&1

echo 6. نسخ App.xaml المحدث...
copy "..\..\App_New.xaml" "App.xaml" >nul 2>&1
copy "..\..\App.xaml.cs" "." >nul 2>&1

echo 7. بناء المشروع الجديد...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم إنشاء المشروع الجديد بنجاح!
    echo.
    echo تشغيل التطبيق الجديد...
    start "نظام إدارة مصنع التمور - نظيف" "bin\Debug\net8.0-windows\DatesFactoryManager.exe"
    echo.
    echo 🎊 مبروك! لديك الآن مشروع نظيف وحديث!
) else (
    echo ❌ فشل في البناء!
)

echo.
echo العودة للمجلد الأصلي...
cd ..\..

pause
