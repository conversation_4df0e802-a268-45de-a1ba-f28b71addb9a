using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class CustomerService : BaseDataService<Customer>, ICustomerService
    {
        public CustomerService(ApplicationDbContext context) : base(context)
        {
        }
        
        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            return await _dbSet
                .Where(c => !c.IsDeleted && c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }
        
        public async Task<Customer?> GetByPhoneAsync(string phone)
        {
            return await _dbSet
                .FirstOrDefaultAsync(c => c.Phone == phone && !c.IsDeleted);
        }
        
        public async Task<decimal> GetCustomerBalanceAsync(int customerId)
        {
            var customer = await GetByIdAsync(customerId);
            return customer?.CurrentBalance ?? 0;
        }
        
        public async Task<bool> UpdateBalanceAsync(int customerId, decimal amount)
        {
            var customer = await GetByIdAsync(customerId);
            if (customer == null) return false;
            
            customer.CurrentBalance += amount;
            await UpdateAsync(customer);
            return true;
        }
        
        public override async Task<IEnumerable<Customer>> GetAllAsync()
        {
            return await _dbSet
                .Where(c => !c.IsDeleted)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }
    }
}
