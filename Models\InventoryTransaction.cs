using System.ComponentModel.DataAnnotations;

namespace DatesFactoryManager.Models
{
    public class InventoryTransaction : BaseEntity
    {
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        public TransactionType Type { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        public decimal TotalValue => Quantity * UnitPrice;
        
        [StringLength(20)]
        public string? ReferenceNumber { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public int? SaleId { get; set; }
        public int? PurchaseId { get; set; }
        
        // Navigation properties
        public virtual Product Product { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual Sale? Sale { get; set; }
        public virtual Purchase? Purchase { get; set; }
    }
    
    public enum TransactionType
    {
        دخول = 1,      // Stock In
        خروج = 2,      // Stock Out
        تعديل = 3,     // Adjustment
        تالف = 4,      // Damaged
        إرجاع = 5      // Return
    }
}
