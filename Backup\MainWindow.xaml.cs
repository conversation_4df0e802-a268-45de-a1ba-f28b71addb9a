﻿using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DatesFactoryManager.Models;
using DatesFactoryManager.Views;

namespace DatesFactoryManager;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private bool _isMenuCollapsed = false;

    public MainWindow()
    {
        InitializeComponent();
        InitializeWindow();
    }

    private void InitializeWindow()
    {
        // Set user info
        var currentUser = Application.Current.Properties["CurrentUser"] as User;
        if (currentUser != null)
        {
            UserNameTextBlock.Text = $"مرحباً، {currentUser.FullName}";
        }

        // Navigate to dashboard by default
        NavigateTo("Dashboard");
    }

    private void MenuButton_Click(object sender, RoutedEventArgs e)
    {
        _isMenuCollapsed = !_isMenuCollapsed;

        if (_isMenuCollapsed)
        {
            SideNavCard.Width = 60;
            // Hide text in buttons, show only icons
            foreach (Button button in FindVisualChildren<Button>(SideNavCard))
            {
                var stackPanel = button.Content as StackPanel;
                if (stackPanel != null && stackPanel.Children.Count > 1)
                {
                    var textBlock = stackPanel.Children[1] as TextBlock;
                    if (textBlock != null)
                    {
                        textBlock.Visibility = Visibility.Collapsed;
                    }
                }
            }
        }
        else
        {
            SideNavCard.Width = 250;
            // Show text in buttons
            foreach (Button button in FindVisualChildren<Button>(SideNavCard))
            {
                var stackPanel = button.Content as StackPanel;
                if (stackPanel != null && stackPanel.Children.Count > 1)
                {
                    var textBlock = stackPanel.Children[1] as TextBlock;
                    if (textBlock != null)
                    {
                        textBlock.Visibility = Visibility.Visible;
                    }
                }
            }
        }
    }

    private void NavigationButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string page)
        {
            NavigateTo(page);
            UpdateNavigationButtons(button);
        }
    }

    private void NavigateTo(string pageName)
    {
        try
        {
            Page? page = pageName switch
            {
                "Dashboard" => new DashboardPage(),
                "Products" => new ProductsPage(),
                "Sales" => new SalesPage(),
                "Purchases" => new PurchasesPage(),
                "Inventory" => new InventoryPage(),
                "Customers" => new CustomersPage(),
                "Suppliers" => new SuppliersPage(),
                "Reports" => new ReportsPage(),
                "Settings" => new SettingsPage(),
                _ => new DashboardPage()
            };

            MainFrame.Navigate(page);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateNavigationButtons(Button selectedButton)
    {
        // Reset all buttons to outlined style
        DashboardButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        ProductsButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        SalesButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        PurchasesButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        InventoryButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        CustomersButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        SuppliersButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        ReportsButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");
        SettingsButton.Style = (Style)FindResource("MaterialDesignOutlinedButton");

        // Set selected button to raised style
        selectedButton.Style = (Style)FindResource("MaterialDesignRaisedButton");
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد",
                                   MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            // Clear current user
            Application.Current.Properties.Remove("CurrentUser");

            // Show login window
            var loginWindow = new LoginWindow();
            loginWindow.Show();

            // Close main window
            this.Close();
        }
    }

    // Helper method to find visual children
    private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
    {
        if (depObj != null)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                if (child != null && child is T)
                {
                    yield return (T)child;
                }

                foreach (T childOfChild in FindVisualChildren<T>(child))
                {
                    yield return childOfChild;
                }
            }
        }
    }
}