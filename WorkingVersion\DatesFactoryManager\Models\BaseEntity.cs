using System.ComponentModel.DataAnnotations;

namespace DatesFactoryManager.Models
{
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        public bool IsSynced { get; set; } = false;
        
        public DateTime? LastSyncedAt { get; set; }
        
        public bool IsDeleted { get; set; } = false;
    }
}
