<Window x:Class="DatesFactoryManager.Views.ModernMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="http://schemas.modernwpf.com/2019"
        Title="نظام إدارة مصنع التمور"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        ui:WindowHelper.UseModernWindowStyle="True">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="{DynamicResource SystemControlHighlightAccentBrush}"
                Height="60">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- App Title -->
                <StackPanel Grid.Column="0"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <ui:FontIcon Glyph="&#xE7F4;"
                               FontSize="24"
                               Foreground="White"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="نظام إدارة مصنع التمور"
                              FontSize="18"
                              FontWeight="SemiBold"
                              Foreground="White"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <!-- User Info -->
                <StackPanel Grid.Column="2"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <ui:FontIcon Glyph="&#xE77B;"
                               FontSize="16"
                               Foreground="White"
                               Margin="0,0,8,0"/>
                    <TextBlock x:Name="UserNameTextBlock"
                              Text="مرحباً، المستخدم"
                              FontSize="14"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="0,0,15,0"/>
                    <Button Content="تسجيل الخروج"
                           Style="{StaticResource DefaultButtonStyle}"
                           Background="Transparent"
                           Foreground="White"
                           BorderBrush="White"
                           Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <Border Grid.Column="0"
                    Background="{DynamicResource SystemControlPageBackgroundChromeMediumLowBrush}"
                    BorderBrush="{DynamicResource SystemControlForegroundBaseLowBrush}"
                    BorderThickness="0,0,1,0">

                <ui:SimpleStackPanel Spacing="5" Margin="10">

                    <!-- Dashboard -->
                    <Button x:Name="DashboardButton"
                           Content="لوحة المعلومات"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Dashboard">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE80F;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <!-- Products -->
                    <Button x:Name="ProductsButton"
                           Content="المنتجات"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Products">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE7C5;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <!-- Sales -->
                    <Button x:Name="SalesButton"
                           Content="المبيعات"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Sales">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE8C8;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <!-- Customers -->
                    <Button x:Name="CustomersButton"
                           Content="العملاء"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Customers">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE77B;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <!-- Inventory -->
                    <Button x:Name="InventoryButton"
                           Content="المخزون"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Inventory">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE7B8;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <!-- Reports -->
                    <Button x:Name="ReportsButton"
                           Content="التقارير"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Reports">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE9F9;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <!-- Settings -->
                    <Button x:Name="SettingsButton"
                           Content="الإعدادات"
                           Style="{StaticResource DefaultButtonStyle}"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Height="40"
                           Click="NavigationButton_Click"
                           Tag="Settings">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xE713;" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                </ui:SimpleStackPanel>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1"
                    Background="{DynamicResource SystemControlPageBackgroundChromeLowBrush}"
                    Padding="20">
                <Frame x:Name="MainFrame"
                       NavigationUIVisibility="Hidden"
                       Background="Transparent"/>
            </Border>

        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="{DynamicResource SystemControlPageBackgroundChromeMediumLowBrush}"
                BorderBrush="{DynamicResource SystemControlForegroundBaseLowBrush}"
                BorderThickness="0,1,0,0"
                Height="30">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="جاهز"
                          VerticalAlignment="Center"
                          FontSize="12"/>

                <TextBlock Grid.Column="1"
                          x:Name="StatusTextBlock"
                          Text="2025/06/19"
                          VerticalAlignment="Center"
                          FontSize="12"/>
            </Grid>
        </Border>

    </Grid>
</Window>
