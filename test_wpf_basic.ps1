# اختبار WPF الأساسي
Write-Host "اختبار WPF..." -ForegroundColor Green

# إنشاء تطبيق WPF بسيط
$code = @'
using System;
using System.Windows;

namespace TestWPF
{
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                MessageBox.Show("بدء اختبار WPF...", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                
                var app = new Application();
                var window = new Window
                {
                    Title = "اختبار WPF - نجح!",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    Content = new System.Windows.Controls.TextBlock
                    {
                        Text = "مرحباً! WPF يعمل بنجاح!",
                        FontSize = 20,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    }
                };
                
                MessageBox.Show("سيتم فتح النافذة الآن...", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                
                app.Run(window);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
'@

# حفظ الكود في ملف
$code | Out-File -FilePath "TestWPF.cs" -Encoding UTF8

# تجميع وتشغيل
Write-Host "تجميع التطبيق..." -ForegroundColor Yellow
$result = csc /target:winexe /reference:"C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.15\PresentationFramework.dll","C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.15\PresentationCore.dll","C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.15\WindowsBase.dll" TestWPF.cs 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم التجميع بنجاح!" -ForegroundColor Green
    Write-Host "تشغيل التطبيق..." -ForegroundColor Yellow
    .\TestWPF.exe
} else {
    Write-Host "❌ فشل التجميع:" -ForegroundColor Red
    Write-Host $result -ForegroundColor Red
}

# تنظيف
Remove-Item "TestWPF.cs" -ErrorAction SilentlyContinue
Remove-Item "TestWPF.exe" -ErrorAction SilentlyContinue
