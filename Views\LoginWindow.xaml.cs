using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using DatesFactoryManager.Services;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Views
{
    public partial class LoginWindow : Window
    {
        private readonly IUserService _userService;
        
        public LoginWindow()
        {
            InitializeComponent();
            _userService = App.ServiceProvider?.GetRequiredService<IUserService>() 
                          ?? throw new InvalidOperationException("UserService not found");
            
            // Set focus to username textbox
            Loaded += (s, e) => UsernameTextBox.Focus();
            
            // Handle Enter key in password box
            PasswordBox.KeyDown += (s, e) =>
            {
                if (e.Key == Key.Enter)
                    LoginButton_Click(s, e);
            };
        }
        
        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Disable login button during authentication
                LoginButton.IsEnabled = false;
                LoginButton.Content = "جاري التحقق...";
                ErrorTextBlock.Visibility = Visibility.Collapsed;
                
                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;
                
                // Validate input
                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    return;
                }
                
                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }
                
                // Authenticate user
                var user = await _userService.AuthenticateAsync(username, password);
                
                if (user != null)
                {
                    // Store current user in application
                    Application.Current.Properties["CurrentUser"] = user;
                    
                    // Open main window
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    
                    // Close login window
                    this.Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                // Re-enable login button
                LoginButton.IsEnabled = true;
                LoginButton.Content = "تسجيل الدخول";
            }
        }
        
        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }
        
        protected override void OnClosed(EventArgs e)
        {
            // If no main window is open, shutdown the application
            if (Application.Current.Windows.Count == 1)
            {
                Application.Current.Shutdown();
            }
            base.OnClosed(e);
        }
    }
}
