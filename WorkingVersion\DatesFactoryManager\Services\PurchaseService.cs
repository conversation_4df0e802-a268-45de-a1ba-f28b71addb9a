using Microsoft.EntityFrameworkCore;
using DatesFactoryManager.Data;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Services
{
    public class PurchaseService : BaseDataService<Purchase>, IPurchaseService
    {
        public PurchaseService(ApplicationDbContext context) : base(context)
        {
        }
        
        public async Task<IEnumerable<Purchase>> GetPurchasesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbSet
                .Include(p => p.Supplier)
                .Include(p => p.User)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.Product)
                .Where(p => !p.IsDeleted && p.PurchaseDate >= startDate && p.PurchaseDate < endDate)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Purchase>> GetPurchasesBySupplierAsync(int supplierId)
        {
            return await _dbSet
                .Include(p => p.Supplier)
                .Include(p => p.User)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.Product)
                .Where(p => !p.IsDeleted && p.SupplierId == supplierId)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }
        
        public async Task<decimal> GetTotalPurchasesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _dbSet.Where(p => !p.IsDeleted && p.Status == PurchaseStatus.مكتملة);
            
            if (startDate.HasValue)
                query = query.Where(p => p.PurchaseDate >= startDate.Value);
                
            if (endDate.HasValue)
                query = query.Where(p => p.PurchaseDate < endDate.Value);
            
            return await query.SumAsync(p => p.TotalAmount);
        }
        
        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var today = DateTime.Today;
            var todayString = today.ToString("yyyyMMdd");
            
            var lastInvoice = await _dbSet
                .Where(p => p.InvoiceNumber.StartsWith($"PUR-{todayString}"))
                .OrderByDescending(p => p.InvoiceNumber)
                .FirstOrDefaultAsync();
            
            int nextNumber = 1;
            if (lastInvoice != null)
            {
                var lastNumberPart = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }
            
            return $"PUR-{todayString}-{nextNumber:D3}";
        }
        
        public async Task<Purchase> CreatePurchaseWithItemsAsync(Purchase purchase, List<PurchaseItem> items)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Generate invoice number if not provided
                if (string.IsNullOrEmpty(purchase.InvoiceNumber))
                {
                    purchase.InvoiceNumber = await GenerateInvoiceNumberAsync();
                }
                
                // Add purchase
                var addedPurchase = await AddAsync(purchase);
                
                // Add purchase items
                foreach (var item in items)
                {
                    item.PurchaseId = addedPurchase.Id;
                    _context.PurchaseItems.Add(item);
                }
                
                await _context.SaveChangesAsync();
                
                // Update product stock
                var productService = new ProductService(_context);
                foreach (var item in items)
                {
                    await productService.UpdateStockAsync(item.ProductId, (int)item.Quantity, TransactionType.دخول);
                }
                
                await transaction.CommitAsync();
                return addedPurchase;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        
        public override async Task<Purchase?> GetByIdAsync(int id)
        {
            return await _dbSet
                .Include(p => p.Supplier)
                .Include(p => p.User)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.Product)
                .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
        }
        
        public override async Task<IEnumerable<Purchase>> GetAllAsync()
        {
            return await _dbSet
                .Include(p => p.Supplier)
                .Include(p => p.User)
                .Where(p => !p.IsDeleted)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }
    }
}
