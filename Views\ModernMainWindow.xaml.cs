using System.Windows;
using System.Windows.Controls;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Views
{
    public partial class ModernMainWindow : Window
    {
        public ModernMainWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Set user info
            var currentUser = Application.Current.Properties["CurrentUser"] as User;
            if (currentUser != null)
            {
                UserNameTextBlock.Text = $"مرحباً، {currentUser.FullName}";
            }

            // Navigate to dashboard by default
            NavigateTo("Dashboard");
        }

        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string pageName)
            {
                NavigateTo(pageName);
            }
        }

        private void NavigateTo(string pageName)
        {
            try
            {
                Page? page = pageName switch
                {
                    "Dashboard" => new ModernDashboardPage(),
                    "Products" => new ModernProductsPage(),
                    "Sales" => new ModernSalesPage(),
                    "Customers" => new ModernCustomersPage(),
                    "Inventory" => new ModernInventoryPage(),
                    "Reports" => new ModernReportsPage(),
                    "Settings" => new ModernSettingsPage(),
                    _ => new ModernDashboardPage()
                };

                MainFrame.Navigate(page);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", 
                                       "تأكيد تسجيل الخروج", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // Clear current user
                Application.Current.Properties["CurrentUser"] = null;
                
                // Open login window
                var loginWindow = new ModernLoginWindow();
                loginWindow.Show();
                
                // Close main window
                this.Close();
            }
        }
    }
}
