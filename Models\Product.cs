using System.ComponentModel.DataAnnotations;

namespace DatesFactoryManager.Models
{
    public class Product : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;
        
        [Required]
        public ProductCategory Category { get; set; }
        
        [Required]
        public ProductQuality Quality { get; set; }
        
        [Required]
        public ProductSize Size { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal PurchasePrice { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal SalePrice { get; set; }
        
        [Required]
        [Range(0, int.MaxValue)]
        public int StockQuantity { get; set; }
        
        [Range(0, int.MaxValue)]
        public int MinimumStock { get; set; } = 10;
        
        [StringLength(50)]
        public string Unit { get; set; } = "كيلو";
        
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<PurchaseItem> PurchaseItems { get; set; } = new List<PurchaseItem>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    }
    
    public enum ProductCategory
    {
        تمور_مجهول = 1,
        تمور_صقعي = 2,
        تمور_خلاص = 3,
        تمور_زهدي = 4,
        تمور_حلاوي = 5,
        تمور_برحي = 6,
        تمور_أخرى = 99
    }
    
    public enum ProductQuality
    {
        ممتاز = 1,
        جيد_جداً = 2,
        جيد = 3,
        متوسط = 4
    }
    
    public enum ProductSize
    {
        كبير = 1,
        متوسط = 2,
        صغير = 3,
        مخلوط = 4
    }
}
