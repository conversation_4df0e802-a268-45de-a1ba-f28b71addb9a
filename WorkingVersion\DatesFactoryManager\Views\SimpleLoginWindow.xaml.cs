using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using DatesFactoryManager.Services;
using DatesFactoryManager.Models;

namespace DatesFactoryManager.Views
{
    public partial class SimpleLoginWindow : Window
    {
        private readonly IUserService _userService;
        
        public SimpleLoginWindow()
        {
            try
            {
                InitializeComponent();
                
                MessageBox.Show("تم تحميل نافذة تسجيل الدخول المبسطة بنجاح!", "نجح!", MessageBoxButton.OK, MessageBoxImage.Information);
                
                _userService = App.ServiceProvider?.GetRequiredService<IUserService>() 
                              ?? throw new InvalidOperationException("UserService not found");
                
                // Set focus to username textbox
                Loaded += (s, e) => UsernameTextBox.Focus();
                
                // Handle Enter key in password box
                PasswordBox.KeyDown += (s, e) =>
                {
                    if (e.Key == Key.Enter)
                        LoginButton_Click(s, e);
                };
                
                // Set default values for testing
                UsernameTextBox.Text = "admin";
                PasswordBox.Password = "admin123";
                
                this.Title = "تسجيل الدخول - نظام إدارة مصنع التمور (تم التحميل بنجاح)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل نافذة تسجيل الدخول: {ex.Message}\n\nتفاصيل: {ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear previous error
                ErrorTextBlock.Visibility = Visibility.Collapsed;
                
                // Get input values
                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;
                
                // Validate input
                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }
                
                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }
                
                // Disable login button during authentication
                LoginButton.IsEnabled = false;
                LoginButton.Content = "جاري التحقق...";
                
                MessageBox.Show($"محاولة تسجيل الدخول باستخدام:\nاسم المستخدم: {username}\nكلمة المرور: {password}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // Authenticate user
                var user = await _userService.AuthenticateAsync(username, password);
                
                if (user != null)
                {
                    MessageBox.Show($"تم تسجيل الدخول بنجاح!\nمرحباً {user.FullName}", "نجح تسجيل الدخول", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // Store current user in application
                    Application.Current.Properties["CurrentUser"] = user;
                    
                    // Open main window
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    
                    // Close login window
                    this.Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
                MessageBox.Show($"تفاصيل الخطأ: {ex.StackTrace}", "تفاصيل الخطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Re-enable login button
                LoginButton.IsEnabled = true;
                LoginButton.Content = "تسجيل الدخول";
            }
        }
        
        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }
        
        protected override void OnClosed(EventArgs e)
        {
            // If no main window is open, shutdown the application
            if (Application.Current.Windows.Count == 1)
            {
                Application.Current.Shutdown();
            }
            base.OnClosed(e);
        }
    }
}
